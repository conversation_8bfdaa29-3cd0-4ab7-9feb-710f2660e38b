/**
 * Custom Address Checkout JavaScript
 * Handles address dropdown functionality on checkout page
 */

var customAddressLoading = 0;

// Ensure jQuery is available
if (typeof jQuery === 'undefined') {
    console.error('jQuery is not loaded! Custom address functionality will not work.');
} else {
    console.log('jQuery is available, version:', jQuery.fn.jquery);
}

jQuery(document).ready(function($) {
    console.log('Custom address script initializing...');

    // Test AJAX availability
    console.log('Testing AJAX setup...');
    console.log('customAddress:', typeof customAddress !== 'undefined' ? customAddress : 'undefined');
    console.log('ajaxurl:', typeof ajaxurl !== 'undefined' ? ajaxurl : 'undefined');

    // Debug: Check if dropdown is inside the checkout form
    setTimeout(function() {
        var dropdown = jQuery('.custom_address_select_menu');
        if (dropdown.length) {
            var form = dropdown.closest('form');
            console.log('Dropdown found:', dropdown.length, 'elements');
            console.log('Dropdown is inside form:', form.length > 0);
            console.log('Form action:', form.attr('action'));
            console.log('Dropdown name attribute:', dropdown.attr('name'));
        } else {
            console.log('No custom address dropdown found');
        }
    }, 1000);

    // Handle address dropdown changes
    jQuery(document).on('change', '.custom_address_select_menu', customOnAddressSelect);

    // Load default addresses on page load
    customLoadDefaultAddresses();

    // Debug: Monitor form submission and ensure YSHIP value is preserved
    jQuery(document).on('submit', 'form.checkout', function(e) {
        var formData = new FormData(this);
        console.log('Form being submitted with address selection:');

        var hasAddressSelection = false;
        for (var pair of formData.entries()) {
            if (pair[0].includes('address_id')) {
                console.log('Form field:', pair[0], '=', pair[1]);
                hasAddressSelection = true;
            }
        }

        // Ensure YSHIP dropdown value is properly set in the form
        var shippingDropdown = jQuery('#custom_address_select_menu_shipping');
        if (shippingDropdown.length && shippingDropdown.val() === 'YSHIP') {
            // Make sure the form field is set correctly
            var hiddenField = jQuery('input[name="custom_shipping_selected_address_id"]');
            if (hiddenField.length) {
                hiddenField.val('add_new');
                console.log('Ensured YSHIP form field is set to add_new');
            }
        }

        if (!hasAddressSelection) {
            console.warn('No address selection found in form submission!');
        }
    });

    // Re-initialize dropdowns after checkout updates
    jQuery(document).on('updated_checkout', function() {
        // Skip if we're currently processing YSHIP to prevent race conditions
        if (jQuery('body').data('yship-processing')) {
            console.log('Skipping checkout update handling - YSHIP processing in progress');
            return;
        }

        console.log('Checkout updated - preserving dropdown values');

        // Preserve selected values before re-initializing
        var selectedValues = {};
        jQuery('.custom_address_select_menu').each(function() {
            var $this = jQuery(this);
            var currentValue = $this.val();
            selectedValues[$this.attr('id')] = currentValue;
            console.log('Preserving value for', $this.attr('id'), ':', currentValue);
        });

        jQuery('.custom_address_select_menu').selectWoo({
            width: 'resolve',
            containerCssClass: "custom-address-select-menu-container",
            dropdownCssClass: "custom-address-select-menu-dropdown"
        });

        // Restore selected values after re-initialization
        jQuery.each(selectedValues, function(id, value) {
            if (value && value !== 'none') {
                console.log('Restoring value for', id, ':', value);
                jQuery('#' + id).val(value).trigger('change.select2');
            }
        });
    });
});

/**
 * Load default addresses on page load
 */
function customLoadDefaultAddresses() {
    var types = ['billing', 'shipping'];
    
    for (var i = 0; i < types.length; i++) {
        var dropdown = jQuery('#custom_address_select_menu_' + types[i]);
        if (dropdown.length && dropdown.val() != null && dropdown.val() != 'none') {
            dropdown.trigger('change');
        }
    }
}

/**
 * Handle address selection change
 */
function customOnAddressSelect(event) {
    if (event.target.value == 'none') {
        return;
    }
    
    var formType = jQuery(event.currentTarget).data('type');
    
    // Handle "Add new" option
    if (event.target.value == 'YSHIP') {
        console.log('YSHIP selected for:', formType);
        console.log('Dropdown value before reset:', jQuery(event.currentTarget).val());

        // Set a flag to prevent race conditions
        jQuery('body').data('yship-processing', true);

        customResetCheckoutInputFields(formType);
        customEnableFormFields(formType);

        // Ensure the dropdown value is preserved
        jQuery(event.currentTarget).val('YSHIP');
        console.log('Dropdown value after setting to YSHIP:', jQuery(event.currentTarget).val());

        // Add validation to ensure required fields are filled before allowing checkout
        customAddYshipValidation(formType);

        // Clear the processing flag and trigger checkout update
        setTimeout(function() {
            jQuery('body').data('yship-processing', false);
            console.log('Triggering checkout update with YSHIP selected');
            jQuery('body').trigger('update_checkout');
        }, 300); // Reduced timeout for better responsiveness
        return;
    }
    
    // Load address data via AJAX
    customLoadAddressData(event.target.value, formType);
}

/**
 * Reset checkout input fields
 */
function customResetCheckoutInputFields(formType) {
    var fields = [
        // Skip first_name and last_name - don't clear them
        // formType + '_first_name',
        // formType + '_last_name',
        formType + '_company',
        formType + '_address_1',
        formType + '_address_2',
        formType + '_city',
        formType + '_postcode',
        formType + '_state',
        formType + '_country'
    ];

    // console.log('Clearing fields for form type:', formType);

    fields.forEach(function(fieldName) {
        var field = jQuery('#' + fieldName);
        if (field.length) {
            // console.log('Clearing field:', fieldName);
            field.val('').trigger('change');
        } else {
            console.log('Field not found:', fieldName);
        }
        field.closest('.form-row').removeClass('disabled-field');
    });

    // Also clear any select2 dropdowns
    fields.forEach(function(fieldName) {
        var field = jQuery('#' + fieldName);
        if (field.length && field.hasClass('select2-hidden-accessible')) {
            field.val('').trigger('change.select2');
        }
    });

    jQuery('#label_shipping_address_id').removeClass('disabled-field');
    jQuery('#shipping_address_2').closest('.form-row').removeClass('disabled-field');
    jQuery('#shipping_company').closest('.form-row').removeClass('disabled-field');
}

/**
 * Enable form fields if they were disabled
 */
function customEnableFormFields(formType) {
    console.log('Enabling form fields for:', formType);

    if (formType == 'shipping') {
        jQuery('.woocommerce-shipping-fields__field-wrapper input, .woocommerce-shipping-fields__field-wrapper span, .woocommerce-shipping-fields__field-wrapper select')
            .css('pointer-events', 'auto')
            .css('background', '')
            .prop('disabled', false);

        // Also enable specific shipping fields (skip first/last name)
        jQuery('#shipping_company, #shipping_address_1, #shipping_address_2, #shipping_city, #shipping_postcode, #shipping_state, #shipping_country')
            .css('pointer-events', 'auto')
            .css('background', '')
            .prop('disabled', false);

    } else if (formType == 'billing') {
        jQuery('.woocommerce-billing-fields__field-wrapper input, .woocommerce-billing-fields__field-wrapper span, .woocommerce-billing-fields__field-wrapper select')
            .css('pointer-events', 'auto')
            .css('background', '')
            .prop('disabled', false);

        // Also enable specific billing fields (skip first/last name)
        jQuery('#billing_company, #billing_address_1, #billing_address_2, #billing_city, #billing_postcode, #billing_state, #billing_country')
            .css('pointer-events', 'auto')
            .css('background', '')
            .prop('disabled', false);
    }

    console.log('Form fields enabled for:', formType);
}

/**
 * Load address data via AJAX
 */
function customLoadAddressData(addressId, formType) {
    customAddressLoading++;
    
    // Show loading state
    customShowLoadingState(formType);
    
    var formData = new FormData();
    formData.append('action', 'custom_get_address_by_id');
    formData.append('address_id', addressId);
    formData.append('security_token', customAddress.security_token);
    
    // Fallback for AJAX URL if customAddress is not defined
    var ajaxUrl = (typeof customAddress !== 'undefined' && customAddress.ajaxurl) ?
                  customAddress.ajaxurl :
                  (typeof ajaxurl !== 'undefined' ? ajaxurl : '/wp-admin/admin-ajax.php');

    var securityToken = (typeof customAddress !== 'undefined' && customAddress.security_token) ?
                        customAddress.security_token :
                        'fallback_token';

    console.log('Using AJAX URL:', ajaxUrl);
    console.log('Using security token:', securityToken);

    // Update the security token in form data
    formData.set('security_token', securityToken);

    jQuery.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            customAddressLoading--;
            customHideLoadingState(formType);

            if (response.success && response.data) {
                customPopulateAddressFields(response.data, formType);
            } else {
                console.error('Failed to load address:', response.data);
            }

            // Trigger checkout update
            if (customAddressLoading === 0) {
                jQuery('body').trigger('update_checkout');
            }
        },
        error: function(xhr, status, error) {
            customAddressLoading--;
            customHideLoadingState(formType);
            console.error('AJAX error:', error);

            if (customAddressLoading === 0) {
                jQuery('body').trigger('update_checkout');
            }
        }
    });
}

/**
 * Populate address fields with loaded data
 */
function customPopulateAddressFields(addressData, formType) {
    console.log('Populating fields for form type:', formType);
    console.log('Address data received:', addressData);

    // Handle array structure - if addressData is an array, get the first element
    var actualAddressData = addressData;
    if (Array.isArray(addressData) && addressData.length > 0) {
        actualAddressData = addressData[0];
        console.log('Address data is array, using first element:', actualAddressData);
    }

    // First, handle the special mapping for address_internal_name to company field
    console.log('Looking for address_internal_name in:', actualAddressData);
    console.log('Available keys:', Object.keys(actualAddressData));

    // Map of address data keys to form field IDs - handle both shipping and billing
    // Skip first_name, last_name, and company (handled above) - don't populate them
    var fieldMapping = {
        // Skip first/last name fields and company (handled separately above)
        // 'shipping_first_name': formType + '_first_name',
        // 'shipping_last_name': formType + '_last_name',
        // 'shipping_company': formType + '_company', // Handled by address_internal_name above
        'shipping_address_1': formType + '_address_1',
        'shipping_address_2': formType + '_address_2',
        'shipping_city': formType + '_city',
        'shipping_postcode': formType + '_postcode',
        'shipping_state': formType + '_state',
        'shipping_country': formType + '_country',
        // Also handle billing fields if formType is billing (skip first/last name and company)
        // 'billing_first_name': formType + '_first_name',
        // 'billing_last_name': formType + '_last_name',
        // 'billing_company': formType + '_company', // Handled by address_internal_name above
        'billing_address_1': formType + '_address_1',
        'billing_address_2': formType + '_address_2',
        'billing_city': formType + '_city',
        'billing_postcode': formType + '_postcode',
        'billing_state': formType + '_state',
        'billing_country': formType + '_country'
    };

    // First, clear all fields
    customResetCheckoutInputFields(formType);

    var companyValue = actualAddressData['address_internal_name'];
    console.log('Found address_internal_name value:', companyValue);
    
    if (companyValue) {
        var companyField = jQuery('#' + formType + '_company');
        // console.log('Company field name:', companyField);
        if (companyField.length) {
            console.log('Setting company field to address_internal_name:', companyValue);
            companyField.val(companyValue);
        }
    } else {
        console.log('address_internal_name not found or empty, checking alternatives...');
        // Try alternative field names that might contain the company/address name
        var alternatives = ['shipping_company', 'billing_company', 'company', 'name', 'address_name'];
        for (var i = 0; i < alternatives.length; i++) {
            var altValue = actualAddressData[alternatives[i]];
            if (altValue) {
                console.log('Found alternative company value in', alternatives[i] + ':', altValue);
                var companyField = jQuery('#' + formType + '_company');
                if (companyField.length) {
                    companyField.val(altValue);
                }
                break;
            }
        }
    }

    // Populate each field
    Object.keys(fieldMapping).forEach(function(dataKey) {
        // Only process fields that match the current form type
        if ((formType === 'shipping' && dataKey.startsWith('shipping_')) ||
            (formType === 'billing' && dataKey.startsWith('billing_'))) {

            var fieldId = fieldMapping[dataKey];
            var fieldValue = actualAddressData[dataKey] || '';
            var field = jQuery('#' + fieldId);

            console.log('Processing field:', dataKey, '-> ', fieldId, 'Value:', fieldValue);

            if (field.length && fieldValue) {
                field.val(fieldValue);
                field.trigger('change');
                field.trigger('input');
                field.trigger('blur');

                console.log('Set field', fieldId, 'to:', fieldValue);
                field.closest('.form-row').addClass('disabled-field');
                
                // Trigger change event for select fields (country/state)
                if (field.is('select')) {
                    field.trigger('change');
                }
            }
            jQuery('#label_shipping_address_id').addClass('disabled-field');
            jQuery('#shipping_address_2').closest('.form-row').addClass('disabled-field');
            jQuery('#shipping_company').closest('.form-row').addClass('disabled-field').trigger('change');        
            setTimeout(function(){
                jQuery('#shipping_company').trigger('change');                        
            }, 100);
        }
    });

    // Handle country change specially to update states
    var countryValue = actualAddressData[formType + '_country'] || actualAddressData['shipping_country'];
    var stateValue = actualAddressData[formType + '_state'] || actualAddressData['shipping_state'];

    if (countryValue) {
        var countryField = jQuery('#' + formType + '_country');
        console.log('Setting country field:', formType + '_country', 'to:', countryValue);

        if (countryField.length) {
            countryField.val(countryValue).trigger('change');

            // Set state after a delay to allow country change to process
            setTimeout(function() {
                if (stateValue) {
                    var stateField = jQuery('#' + formType + '_state');
                    console.log('Setting state field:', formType + '_state', 'to:', stateValue);
                    stateField.val(stateValue).trigger('change');
                }
            }, 1000); // Increased delay to ensure country processing completes
        }
    }

    // Trigger checkout update after population
    setTimeout(function() {
        jQuery('body').trigger('update_checkout');
    }, 1500);
}

/**
 * Add validation for YSHIP addresses to ensure required fields are filled
 */
function customAddYshipValidation(formType) {
    console.log('Adding YSHIP validation for:', formType);

    // Remove any existing validation
    jQuery('body').off('checkout_error.yship_validation');

    // Add validation that runs before checkout submission
    jQuery('body').on('checkout_error.yship_validation', function() {
        var dropdown = jQuery('#custom_address_select_menu_' + formType);
        if (dropdown.val() === 'YSHIP') {
            var requiredFields = [
                formType + '_first_name',
                formType + '_last_name',
                formType + '_company',
                formType + '_address_1',
                formType + '_city',
                formType + '_postcode',
                formType + '_country'
            ];

            var missingFields = [];
            requiredFields.forEach(function(fieldName) {
                var field = jQuery('#' + fieldName);
                if (field.length && !field.val().trim()) {
                    missingFields.push(fieldName.replace(formType + '_', '').replace('_', ' '));
                }
            });

            if (missingFields.length > 0) {
                console.error('YSHIP validation failed - missing fields:', missingFields);
                return false;
            }
        }
        return true;
    });
}

/**
 * Show loading state
 */
function customShowLoadingState(formType) {
    var container = jQuery('.custom_address_selector_container');
    if (container.length) {
        container.addClass('loading');
        container.append('<div class="custom-address-loading">Loading address...</div>');
    }
}

/**
 * Hide loading state
 */
function customHideLoadingState(formType) {
    var container = jQuery('.custom_address_selector_container');
    if (container.length) {
        container.removeClass('loading');
        container.find('.custom-address-loading').remove();
    }
}
